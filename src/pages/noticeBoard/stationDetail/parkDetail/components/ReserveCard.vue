<template>
  <view class="px-8 py-6 bg-white mt-3">
    <ACellItem title="可预约车位" rightText="查看预约" @rightClick="jumpToReserve"></ACellItem>
    <view class="flex-between items-center w-full mt-6">
      <ProgressInfo
        class="w-full"
        className="w-full"
        :progressStatus="getProgressStatus(reserveConfig?.spaceStatus || '')"
        :leftNum="reserveConfig?.curReservableSpaces || 0"
        :totalNum="reserveConfig?.maxReservableSpaces || 0"
      />
    </view>
  </view>
</template>
<script lang="ts" setup>
import ACellItem from '@/components/ACellItem/index.vue';
import { getProgressStatus } from '@/utils/stationStatus';
import ProgressInfo from '../../components/ProgressInfo.vue';

const props = withDefaults(
  defineProps<{
    reserveConfig: any;
  }>(),
  { reserveConfig: () => ({}) },
);
const emits = defineEmits([]);

const jumpToReserve = () => {
  uni.navigateTo({
    url: `/pages/reserve/detail/index?parkId=${props.reserveConfig.parkingId}`,
  });
};
</script>
