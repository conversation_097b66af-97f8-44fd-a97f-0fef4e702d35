<route lang="json5">
{
  layout: false,
  style: {
    navigationBarTitleText: '停车详情页',
  },
}
</route>
<template>
  <view class="h-100vh pb-safe-40 flex-center box-border" v-if="isQuerying || isError">
    <AEmpty type="loading" v-if="isQuerying" />
    <AEmpty type="net" :button="true" buttonText="刷新" @buttonClick="refreshPage" v-else />
  </view>
  <view class="pb-safe-60" v-else @touchmove="clearTooltip">
    <HeaderSwiper :imgs="parkDetail.imageUrl || []" />
    <view class="bg-page-background mt--8 rounded-t-8 of-hidden relative z-2">
      <StationInfo
        :name="parkDetail.parkName || ''"
        :address="parkDetail.address || ''"
        :runTime="parkDetail.openTimeDescription || ''"
      />
      <LeftInfo :stationInfo="parkDetail" @refresh="getStationDetail" />
      <ReserveCard :reserveConfig="parkDetail.reserveConfig" />
      <ParkPrediction
        v-if="shortPrediction.length && longPrediction.length"
        :shortPrediction="shortPrediction"
        :longPrediction="longPrediction"
        @clearTooltip="clearTooltip"
      />
      <DetailFooter
        :collectStatus="parkDetail.hasCollect || false"
        :tel="parkDetail.contactNumbers || ''"
        type="park"
        :distance="parkDetail.distance ?? undefined"
        :remark="parkDetail.memo || ''"
        @changeCollect="changeCollect"
        @nav="handleNav"
      />
    </view>
  </view>
</template>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import AEmpty from '@/components/AEmpty/AEmpty.vue';
import { DEFAULTGEO } from '@/config';
import { useShare } from '@/hooks/useShare';
import {
  BaseParkDetailVO,
  ParkingLotInquiryService,
  UserCollectionYardService,
} from '@/parkService';
import { ForecastQueryService } from '@/service';
import { useUserStore } from '@/store';
import { isEmpty } from '@/utils/is';
import { getLocation, showSingleToast } from '@/utils/jsapi';
import DetailFooter from '../components/DetailFooter.vue';
import HeaderSwiper from '../components/HeaderSwiper.vue';
import StationInfo from '../components/StationInfo.vue';
import LeftInfo from './components/LeftInfo.vue';
import ParkPrediction from './components/ParkPrediction.vue';
import ReserveCard from './components/ReserveCard.vue';

const { isLogin } = storeToRefs(useUserStore());
const parkId = ref('');
const parkDetail = ref<BaseParkDetailVO>({});
const userLocation = ref({
  latitude: DEFAULTGEO.latitude,
  longitude: DEFAULTGEO.longitude,
});
const isQuerying = ref(true);
const isError = ref(false);
const shortPrediction = ref([]);
const longPrediction = ref([]);
onLoad(async (options: any) => {
  parkId.value = options.parkId;
  getPrediction();
  await getUserLocation();
  await getStationDetail();
});
watch(
  () => isLogin.value,
  (newVal) => {
    if (newVal) {
      getStationDetail();
    }
  },
);
const getUserLocation = async () => {
  const { longitude, latitude, isDefault } = await getLocation();
  userLocation.value = {
    longitude,
    latitude,
  };
};
const getStationDetail = async () => {
  const [err, res] = await ParkingLotInquiryService.postParkDetail({
    parkId: parkId.value,
    longitude: userLocation.value.longitude,
    latitude: userLocation.value.latitude,
  });
  isQuerying.value = false;
  if (res && res.data) {
    parkDetail.value = {
      ...res.data,
      imageUrl: isEmpty(res.data.imageUrl)
        ? [`${import.meta.env.VITE_ALI_OSS_URL_PREFIX}/images/charging/park-detail.png`]
        : res.data.imageUrl,
    };
  } else {
    isError.value = true;
  }
};
const getPrediction = async () => {
  const [err, res] = await ForecastQueryService.getPredictionPark(parkId.value);
  if (res?.data) {
    shortPrediction.value = res.data.shortPrediction || [];
    longPrediction.value = res.data.longPrediction || [];
  }
};
const goCollect = async (value: string) => {
  const [err, res] = await UserCollectionYardService.postCollectionCollectionPark({
    parkId: parkId.value,
    collectionRemark: value,
  });
  if (err) {
    showSingleToast('收藏失败');
  } else {
    showSingleToast('收藏成功');
    getStationDetail();
  }
};
const cancelCollect = async () => {
  const [err, res] = await UserCollectionYardService.postCollectionUnCollectionPark({
    parkIds: [parkId.value],
  });
  if (err) {
    showSingleToast('取消收藏失败');
  } else {
    showSingleToast('取消收藏成功');
    getStationDetail();
  }
};
const changeCollect = (value: string) => {
  if (parkDetail.value.hasCollect) {
    cancelCollect();
  } else {
    goCollect(value);
  }
};
const handleNav = () => {
  if (parkDetail.value.latitude && parkDetail.value.longitude) {
    uni.openLocation({
      latitude: Number(parkDetail.value.latitude),
      longitude: Number(parkDetail.value.longitude),
      name: parkDetail.value.parkName,
      address: parkDetail.value.address,
    });
  }
};
const refreshPage = () => {
  isQuerying.value = true;
  isError.value = false;
  getStationDetail();
};
const shareInfo = computed(() => ({
  title: parkDetail.value.parkName,
  desc: parkDetail.value.address,
}));
const { onShareAppMessage, onShareTimeline } = useShare(shareInfo);
const clearTooltip = () => {
  uni.$emit('clearTooltip');
};
</script>
