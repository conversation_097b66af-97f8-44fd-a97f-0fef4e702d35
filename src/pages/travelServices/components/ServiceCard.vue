<template>
  <view class="service-card-wrapper" @click="handleService">
    <view class="service-card">
      <view>
        <OSSImg
          className="shrink-0"
          :width="120"
          :height="120"
          :src="`/images/travelServices/${value.icon}.png`"
        />
      </view>
      <text class="mt-24rpx text-32rpx font-medium leading-40rpx">{{ value.name }}</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import OSSImg from '@/components/OSSImg/index.vue';
import { checkLogin } from '@/hooks/useCheckLogin';
import { chargeScanTap, scanNoPlate, scanPay } from '@/utils/scanHandler';

const props = withDefaults(
  defineProps<{
    value: { [key: string]: any };
  }>(),
  {
    value: () => ({}),
  },
);

// 处理服务业务
const handleService = () => {
  const data = props.value;
  switch (data.icon) {
    case 'no-plate':
      scanNoPlate();
      break;
    case 'scan':
      chargeScanTap();
      break;
    case 'pay':
      scanPay();
      break;
    case 'input-num':
      jumpToInput();
      break;
    case 'toilets':
      jumpToToilet();
      break;
    case 'garden':
      jumpToGarden();
      break;
    case 'reserve':
      jumpToReserve();
      break;
    default:
      if (data.link) {
        goBicycleLink(data.link);
      } else {
        console.warn('未知操作');
      }
      break;
  }
};
// 跳转到预约停车
const jumpToReserve = () => {
  uni.navigateTo({
    url: '/pages/reserve/list/index',
  });
};

const goBicycleLink = async (link: string) => {
  await checkLogin();
  uni.navigateTo({
    url: `/pages/common/webview/index?url=${encodeURIComponent(link)}&needAuth=Y`,
  });
};

// 跳转输入终端编号页面
const jumpToInput = () => {
  uni.navigateTo({
    url: '/pages/travelServices/terminalInput/index',
  });
};

// 跳转找厕所页面
const jumpToToilet = () => {
  uni.navigateTo({
    url: '/pages/travelServices/findService/index?type=TOILET',
  });
};

// 跳转找公园页面
const jumpToGarden = () => {
  uni.navigateTo({
    url: '/pages/travelServices/findService/index?type=PARK',
  });
};
</script>

<style scoped lang="scss">
.service-card-wrapper {
  @apply relative;

  &::before {
    z-index: 1;
    padding: 4rpx; // 边框宽度
    pointer-events: none;
    content: '';
    background: linear-gradient(180deg, rgb(255 255 255 / 0%) 0%, #fff 99%);
    mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    mask-composite: exclude;

    @apply absolute inset-0 rounded-[24rpx];
  }
}

.service-card {
  @apply h-296rpx rounded-[24rpx] relative;

  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 56rpx 0;
  background: rgb(255 255 255 / 60%);
  backdrop-filter: blur(90px);
}
</style>
