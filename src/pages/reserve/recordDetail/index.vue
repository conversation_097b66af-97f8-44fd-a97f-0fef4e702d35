<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '预约详情',
    'mp-weixin': {
      navigationStyle: 'custom',
    },
    'mp-alipay': {
      transparentTitle: 'always',
    },
    'app-plus': {
      titleNView: {
        type: 'transparent',
        titleText: '预约详情',
      },
    },
  },
}
</route>

<template>
  <view class="common-header-bg h-100vh flex flex-col">
    <TransparentTitle>
      <OSSImg
        :width="48"
        :height="48"
        :src="`/images/reserve/${getStatusIcon(reservationDetail?.status)}.png`"
      />
      <view class="page-title">{{ getStatusText(reservationDetail?.status) }}</view>
    </TransparentTitle>
    <view class="z-3 flex-1 overflow-auto px-6 pb-6 box-border">
      <!-- 加载状态 -->
      <view v-if="isLoading" class="flex justify-center items-center mt-50">
        <text class="text-secondary-26 text-#999">加载中...</text>
      </view>

      <!-- 错误状态 -->
      <view v-else-if="error" class="flex justify-center items-center mt-50">
        <text class="text-secondary-26 text-#999">加载失败，请重试</text>
      </view>

      <!-- 详情内容 -->
      <template v-else-if="reservationDetail">
        <!-- 停车场信息卡片 -->
        <view class="bg-white rounded-12rpx p-6 mb-4 mt-24rpx">
          <view class="flex justify-between items-center">
            <text
              class="text-primary-28 font-medium text-text-primary whitespace-nowrap overflow-hidden text-ellipsis"
            >
              {{ reservationDetail.parkingName || '停车场' }}
            </text>
            <OSSImg :width="32" :height="32" src="/images/reserve/arrow.png" />
          </view>
          <view class="flex items-center mt-3">
            <OSSImg :width="24" :height="24" src="/images/reserve/location.png" className="mr-2" />
            <text class="text-secondary-26 text-#999 truncate">
              {{ reservationDetail.parkingAddress || '停车场地址' }}
            </text>
          </view>
          <view class="h-1rpx w-full bg-#eee mt-5 mb-2"></view>
          <view class="flex justify-between items-center py-3">
            <text class="text-secondary-26 text-#999">车牌号</text>
            <text class="text-secondary-26 text-#111">
              {{ reservationDetail.plateNo || '--' }}
              {{
                getPlateColorText(reservationDetail.plateColor)
                  ? ` (${getPlateColorText(reservationDetail.plateColor)})`
                  : ''
              }}
            </text>
          </view>
          <view class="flex justify-between items-center py-3">
            <text class="text-secondary-26 text-#999">预约入场时间</text>
            <text class="text-secondary-26 text-#111">
              {{ reservationDetail.reservationTimeShow || '--' }}
            </text>
          </view>
        </view>

        <!-- 订单信息卡片 -->
        <view class="bg-white rounded-12rpx p-6 mb-4">
          <view class="flex justify-between items-start py-3">
            <text class="text-secondary-26 text-#999 whitespace-nowrap flex-shrink-0">
              预约记录ID
            </text>
            <view class="flex items-start min-w-0 flex-1 ml-4">
              <text class="text-secondary-26 text-#111 break-all text-right flex-1 mr-2">
                {{ reservationDetail.id || '--' }}
              </text>
              <text
                v-if="reservationDetail.id"
                class="text-secondary-26 text-#56be66 whitespace-nowrap flex-shrink-0"
                @click="copyOrderId(reservationDetail.id)"
              >
                复制
              </text>
            </view>
          </view>
          <view class="flex justify-between items-center py-3">
            <text class="text-secondary-26 text-#999">预约时间</text>
            <text class="text-secondary-26 text-#111">
              {{ reservationDetail.createTime || '--' }}
            </text>
          </view>
          <view class="flex justify-between items-center py-3">
            <text class="text-secondary-26 text-#999">联系手机</text>
            <text class="text-secondary-26 text-#111">{{ reservationDetail.mobile || '--' }}</text>
          </view>

          <!-- 取消相关信息（仅在已取消状态显示） -->
          <template v-if="reservationDetail.status === 2 && reservationDetail.cancelTime">
            <view class="h-1rpx w-full bg-#eee my-2"></view>
            <view class="flex justify-between items-center py-3">
              <text class="text-secondary-26 text-#999">取消时间</text>
              <text class="text-secondary-26 text-#111">{{ reservationDetail.cancelTime }}</text>
            </view>
            <view class="flex justify-between items-center py-3">
              <text class="text-secondary-26 text-#999">取消来源</text>
              <text class="text-secondary-26 text-#111">
                {{ getCancelSourceText(reservationDetail.cancelType) }}
              </text>
            </view>
          </template>

          <!-- 履约相关信息（仅在已履约状态显示） -->
          <template v-if="reservationDetail.status === 4 && reservationDetail.actualEntryTime">
            <view class="h-1rpx w-full bg-#eee my-2"></view>
            <view class="flex justify-between items-center py-3">
              <text class="text-secondary-26 text-#999">实际入场时间</text>
              <text class="text-secondary-26 text-#111">
                {{ reservationDetail.actualEntryTime }}
              </text>
            </view>
          </template>
        </view>

        <!-- 服务选项卡片 -->
        <view v-if="reservationDetail.status === 1" class="w-full flex flex-col bg-white pb-6">
          <view class="text-28rpx leading-40rpx text-#111 py-24rpx text-center">
            您可能需要以下服务
          </view>

          <!-- 服务项 - 地图导航 -->
          <view class="flex justify-center w-full my-10rpx" @click="handleNavigation">
            <view class="flex items-center justify-center">
              <view class="text-28rpx leading-40rpx text-#56be66">地图导航</view>
              <view class="ml-2 relative">
                <OSSImg src="/images/reserve/arrow.png" width="24" height="24" />
              </view>
            </view>
          </view>

          <!-- 服务项 - 预约车停车指引 -->
          <view class="flex justify-center w-full my-10rpx" @click="handleGuidance">
            <view class="flex items-center justify-center">
              <view class="text-28rpx leading-40rpx text-#56be66">预约车停车指引</view>
              <view class="ml-2 relative">
                <OSSImg src="/images/reserve/arrow.png" width="24" height="24" />
              </view>
            </view>
          </view>
        </view>

        <!-- 取消预约按钮（仅在已预约状态显示） -->
        <view
          v-if="reservationDetail.status === 1"
          class="box-border w-136rpx h-56rpx mx-auto my-30rpx text-26rpx leading-56rpx text-#56be66 text-center border-1rpx border-solid border-#56be66 rounded-29rpx"
          @click="handleCancelReservation"
        >
          取消预约
        </view>
      </template>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import TransparentTitle from '@/components/TransparentTitle/index.vue';
import OSSImg from '@/components/OSSImg/index.vue';
import {
  ParkingReserveService,
  type UserReservationRecordDetail,
  type ReservationCancelDTO,
} from '@/parkService';

// 获取页面参数
const params = defineProps({
  id: {
    type: String,
    default: '',
  },
});

// 预约详情数据
const reservationDetail = ref<UserReservationRecordDetail | null>(null);
const isLoading = ref(false);
const error = ref(false);

// 获取预约详情
const getReservationDetail = async () => {
  if (!params.id) {
    error.value = true;
    return;
  }

  isLoading.value = true;
  error.value = false;

  try {
    const [err, res] = await ParkingReserveService.getReservationDetail(params.id);

    if (err || !res?.data) {
      error.value = true;
      uni.showToast({
        title: '获取详情失败',
        icon: 'none',
      });
      return;
    }

    reservationDetail.value = res.data;
  } catch (e) {
    error.value = true;
    uni.showToast({
      title: '获取详情失败',
      icon: 'none',
    });
  } finally {
    isLoading.value = false;
  }
};

// 页面加载时获取详情
onMounted(() => {
  getReservationDetail();
});

// 获取状态显示文本
const getStatusText = (status?: number) => {
  const statusMap = {
    0: '初始',
    1: '已预约',
    2: '已取消',
    3: '已爽约',
    4: '已履约',
  } as const;
  if (status === undefined || status === null) return '未知';
  return statusMap[status as keyof typeof statusMap] || '未知';
};
// 获取状态显示icon
const getStatusIcon = (status?: number) => {
  const iconMap = {
    0: 'reserved',
    1: 'reserved',
    2: 'done',
    3: 'cancelled',
    4: 'done',
  } as const;
  if (status === undefined || status === null) return 'cancelled';
  return iconMap[status as keyof typeof iconMap] || 'cancelled';
};

// 获取车牌颜色显示文本
const getPlateColorText = (plateColor?: string) => {
  const colorMap = {
    BLUE: '蓝',
    YELLOW: '黄',
    GREEN: '绿',
  } as const;

  if (!plateColor) return '';
  return colorMap[plateColor as keyof typeof colorMap] || plateColor;
};

// 获取取消来源显示文本
const getCancelSourceText = (cancelType?: number) => {
  if (cancelType === 1) return '用户主动取消';
  if (cancelType === 2) return '系统取消';
  return '未知';
};

// 处理地图导航
const handleNavigation = () => {
  // 地图导航功能实现
  uni.showToast({
    title: '正在开发中',
    icon: 'none',
  });
};

// 处理停车指引
const handleGuidance = () => {
  // 停车指引功能实现
  uni.showToast({
    title: '正在开发中',
    icon: 'none',
  });
};

// 复制订单号
const copyOrderId = (orderId: string) => {
  uni.setClipboardData({
    data: orderId,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'none',
      });
    },
  });
};

// 取消预约
const handleCancelReservation = () => {
  if (!reservationDetail.value?.id) {
    uni.showToast({
      title: '预约信息异常',
      icon: 'none',
    });
    return;
  }

  uni.showModal({
    title: '确认取消',
    content: '确定要取消此预约吗？',
    confirmColor: '#56be66',
    success: async (res) => {
      if (res.confirm) {
        await cancelReservation();
      }
    },
  });
};

// 执行取消预约请求
const cancelReservation = async () => {
  if (!reservationDetail.value?.id) return;

  uni.showLoading({
    title: '取消中...',
  });

  try {
    const cancelData: ReservationCancelDTO = {
      recordId: reservationDetail.value.id,
    };

    const [err, res] = await ParkingReserveService.postReservationCancel(cancelData);

    if (err || !res?.success) {
      uni.showToast({
        title: res?.subMsg || '取消失败',
        icon: 'none',
      });
      return;
    }

    uni.showToast({
      title: '取消成功',
      icon: 'success',
    });

    // 重新获取详情数据
    await getReservationDetail();
  } catch (e) {
    uni.showToast({
      title: '取消失败',
      icon: 'none',
    });
  } finally {
    uni.hideLoading();
  }
};
</script>

<style scoped lang="scss">
.page-title {
  display: flex;
  justify-content: center;
  width: 100%;
}
</style>
