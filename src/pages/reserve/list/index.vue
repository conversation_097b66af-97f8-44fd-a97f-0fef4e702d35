<route lang="json5">
{
  layout: 'page',
  style: {
    navigationBarTitleText: '附近',
    navigationStyle: 'custom',
    enablePullDownRefresh: false,
    disableScroll: true,
    'mp-alipay': {
      allowsBounceVertical: 'NO',
      transparentTitle: 'always',
    },
  },
}
</route>
<template>
  <view
    v-if="!currentType || !mapCenterLoc.latitude || !mapCenterLoc.longitude"
    class="h-screen w-screen flex-center"
  >
    <AEmpty type="loading" />
  </view>
  <view v-else>
    <TransparentTitle title="预约停车" />
    <NearbyMap :show-traffic="showTraffic" :type="currentType" :pannel-height="pannelHeight" />
    <template v-if="showFixedInfo">
      <HeaderTabSearch
        :tabs-list="tabsList"
        :current-index="currentTabIndex"
        @change="onTabChange"
      />
      <MapTrafficStatus />
      <view class="fixed right-6 z-5" :style="{ bottom: `${pannelHeight}px` }">
        <view @click="changeShowTraffic(!showTraffic)">
          <OSSImg
            :width="62"
            :height="62"
            :src="`/images/nearby/road-condition${showTraffic ? '-active' : ''}.png`"
          />
        </view>
        <view class="mt-6" @click="clickLocBtn">
          <OSSImg :width="62" :height="62" src="/images/nearby/locateButton.png" />
        </view>
      </view>
    </template>
    <NearbyList
      ref="nearbyListRef"
      :type="currentType"
      @change-show-fixed-info="changeShowFixedInfo"
    />
  </view>
</template>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import AEmpty from '@/components/AEmpty/AEmpty.vue';
import OSSImg from '@/components/OSSImg/index.vue';
import TransparentTitle from '@/components/TransparentTitle/index.vue';
import { useNearbyStore } from '@/store/nearbyStore';
import { debounce } from '@/utils/debounce';
import { updateCanGetLoc } from '@/utils/jsapi';
import HeaderTabSearch from './components/HeaderTabSearch.vue';
import MapTrafficStatus from './components/MapTrafficStatus.vue';
import NearbyList from './components/NearbyList.vue';
import NearbyMap from './components/NearbyMap.vue';

const { mapCenterLoc } = storeToRefs(useNearbyStore());
const { resetLoc } = useNearbyStore();
const nearbyListRef = ref();
const showCharge = !import.meta.env.VITE_HIDE_CHARGE;
const tabsList = ref([{ label: '预约停车', value: 'park' }]);
const currentType = ref<'' | 'park' | 'charge'>('');
const currentTabIndex = ref(0);
const showFixedInfo = ref(true);
const firstLoad = ref(true);
const changeShowFixedInfo = (val: boolean) => {
  showFixedInfo.value = val;
};
const pannelHeight = computed(() => {
  return nearbyListRef.value?.height || 0;
});
onLoad((options: any) => {
  if (showCharge) {
    const type = options.type || uni.getStorageSync('nearbyType') || 'park';
    const index = tabsList.value.findIndex((item) => item.value === type);
    if (index >= 0) {
      currentType.value = type;
      currentTabIndex.value = index;
    } else {
      currentType.value = 'park';
    }
    uni.removeStorageSync('nearbyType');
  } else {
    currentType.value = 'park';
  }
  resetLoc();
  firstLoad.value = false;
});
const clickLocBtn = () => {
  updateCanGetLoc(true);
  debounce(resetLoc, 200);
};
onShow(() => {
  if (!firstLoad.value && showCharge) {
    const type = uni.getStorageSync('nearbyType');
    console.error('type', type);
    if (type) {
      const index = tabsList.value.findIndex((item) => item.value === type);
      if (index >= 0) {
        currentType.value = type;
        currentTabIndex.value = index;
      }
      uni.removeStorageSync('nearbyType');
    }
  }
});
const onTabChange = (index: number) => {
  currentTabIndex.value = index;
  currentType.value = tabsList.value[index].value;
};
const showTraffic = ref(false);
const changeShowTraffic = (val: boolean) => {
  showTraffic.value = val;
};
</script>
<style lang="scss" scoped></style>
