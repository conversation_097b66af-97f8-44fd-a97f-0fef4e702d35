<template>
  <view
    v-if="keepSpace || onlyKeepSpace"
    :style="{
      height: statusBarHeight + titleBarHeight + 'px',
    }"
  ></view>
  <!-- #ifdef MP-WEIXIN || APP -->
  <view
    v-if="!onlyKeepSpace"
    class="fly-navbar flex p-x-6 justify-start grid gap-x-1 items-center"
    :style="{
      paddingTop: statusBarHeight + 'px',
      height: titleBarHeight + 'px',
      background: `rgba(255, 255, 255, ${transparency})`,
    }"
  >
    <!-- 1/3，多于1个页面，用返回图标 -->
    <navigator
      v-if="pages.length > 1 && showIcon"
      open-type="navigateBack"
      class="left-icon flex flex-center"
    >
      <view class="i-carbon-chevron-left text-current"></view>
    </navigator>
    <!-- 2/3，只有1个页面，如果不是tabbar，需要首页图标 -->
    <!-- 这种情况一般出现在用户直接打开分享出去的详情页面，或者使用redirectTo等API -->
    <navigator
      v-else-if="!isTabBar && showIcon"
      open-type="switchTab"
      url="/pages/home/<USER>"
      class="left-icon"
    >
      <view class="i-carbon-home text-current"></view>
    </navigator>
    <!-- 3/3，如果当前页就是tabbar页，不用去首页，也就是什么图标都不需要 -->
    <view class="title">
      <slot>{{ title }}</slot>
    </view>
  </view>
  <!-- #endif -->
</template>
<script setup lang="ts">
import useNavbarWeixin from '@/hooks/useNavbar';

withDefaults(
  defineProps<{
    title: string;
    // 占据空间
    keepSpace?: boolean;
    onlyKeepSpace?: boolean;
    showIcon?: boolean;
  }>(),
  {
    title: import.meta.env.VITE_APP_TITLE,
    keepSpace: true,
    onlyKeepSpace: false,
    showIcon: true,
  },
);

const {
  pages,
  isTabBar,
  onScrollToLower,
  safeAreaInsets,
  transparency,
  statusBarHeight,
  titleBarHeight,
} = useNavbarWeixin();
</script>
<style scoped lang="scss">
.fly-navbar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9;
  width: 750rpx;
  color: #000;
  background-color: transparent;

  .left-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48rpx;
    font-size: 44rpx;
    color: #000;
  }

  .title {
    display: flex;
    align-items: center;
    height: 44px;
    font-size: 36rpx;
    font-weight: bold;
  }
}
</style>
